{
	"files.exclude": {
		"**/.git": true,
		"**/.svn": true,
		"**/.hg": true,
		"**/CVS": true,
		"**/.DS_Store": true,
		"**/*.o": true,
		"**/*.exe": true,
	},
	"files.associations": {
		"array": "cpp",
		"atomic": "cpp",
		"bit": "cpp",
		"cctype": "cpp",
		"clocale": "cpp",
		"cmath": "cpp",
		"compare": "cpp",
		"concepts": "cpp",
		"cstdarg": "cpp",
		"cstddef": "cpp",
		"cstdint": "cpp",
		"cstdio": "cpp",
		"cstdlib": "cpp",
		"cwchar": "cpp",
		"cwctype": "cpp",
		"deque": "cpp",
		"string": "cpp",
		"unordered_map": "cpp",
		"vector": "cpp",
		"exception": "cpp",
		"algorithm": "cpp",
		"functional": "cpp",
		"iterator": "cpp",
		"memory": "cpp",
		"memory_resource": "cpp",
		"numeric": "cpp",
		"optional": "cpp",
		"random": "cpp",
		"string_view": "cpp",
		"system_error": "cpp",
		"tuple": "cpp",
		"type_traits": "cpp",
		"utility": "cpp",
		"initializer_list": "cpp",
		"iosfwd": "cpp",
		"limits": "cpp",
		"new": "cpp",
		"numbers": "cpp",
		"ostream": "cpp",
		"stdexcept": "cpp",
		"streambuf": "cpp",
		"cinttypes": "cpp",
		"typeinfo": "cpp",
		"iostream": "cpp",
		"istream": "cpp",
		"*.m": "cpp"
	}
}