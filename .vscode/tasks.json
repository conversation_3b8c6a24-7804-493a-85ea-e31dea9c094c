{
    // See https://go.microsoft.com/fwlink/?LinkId=733558
    // for the documentation about the tasks.json format
    "version": "2.0.0",
    "tasks": [
        {
            "label": "build debug",
            "type": "process",
            "command": "make",
            "windows": {
                "command": "mingw32-make.exe",
            },
            "osx": {
                "args": [
                    "config=debug_arm64"
                ],
            },
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "problemMatcher": [
                "$gcc"
            ],
            "dependsOn":["UpdateMake"]
        },
        {
            "label": "build release",
            "type": "process",
            "command": "make",
            "windows": {
                "command": "mingw32-make.exe",
                "args": [
                    "config=release_x64"
                ],
            },
            "linux": {
                "args": [
                    "config=release_x64"
                ],
            },
            "osx": {
                "args": [
                    "config=release_arm64"
                ],
            },
            "group": "build",
            "problemMatcher": [
                "$gcc"
            ],
            "dependsOn":["UpdateMake"]
        },
        {
            "label": "Clean",
            "type": "process",
            "command": "make",
            "windows": {
                "command": "mingw32-make.exe",
                "args": [
                    "clean"
                ],
            },
            "linux": {
                "args": [
                    "clean"
                ],
            },
            "osx": {
                "args": [
                    "clean"
                ],
            },
            "group": "build",
            "problemMatcher": [
                "$gcc"
            ],
            "dependsOn":["UpdateMake"]
        },
        {
            "label": "UpdateMake",
            "type": "process",
            "command": "./premake5",
            "options": {
                "cwd": "${workspaceFolder}/build/"
            },
            "args": [
                "gmake2"
            ],
            "windows": {
                "command": "./premake5.exe"
            },
            "linux": {
                "command": "./premake5"
            },
            "osx": {
                "command": "premake5.osx"
            },
            "group": "build"
        }
    ]
}
